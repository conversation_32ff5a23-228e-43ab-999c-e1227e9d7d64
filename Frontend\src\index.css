@tailwind base;
@tailwind components;
@tailwind utilities;

/* Base layout fixes */
html, body, #root {
  height: 100vh;
  width: 100vw;
  margin: 0;
  padding: 0;
  overflow: hidden;
}

/* Custom scrollbar for scrollable containers */
.custom-scrollbar {
  overflow-y: auto;
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */
}

.custom-scrollbar::-webkit-scrollbar {
  display: none; /* Chrome, Safari, Opera */
}

/* Add smooth scrolling */
html {
  scroll-behavior: smooth;
}

/* Auth Pages Styling */
@layer components {
  /* Page Layout */
  .auth-page-container {
    @apply relative min-h-[calc(100vh-5rem)] flex items-center justify-center w-full overflow-hidden
    bg-gradient-to-b from-base-100 to-base-200;
  }

  .auth-content-wrapper {
    @apply flex w-full md:flex-row flex-col;
  }

  /* Decorative Elements */
  .auth-decoration-circle {
    @apply absolute w-64 h-64 rounded-full bg-gradient-to-r from-primary/10 to-secondary/10
    blur-xl opacity-70 -z-10;
  }

  /* Form Container */
  .auth-form-container {
    @apply flex-1 flex items-center justify-center p-4 md:p-6 relative z-10;
  }

  .auth-form-card {
    @apply w-full max-w-md space-y-6 bg-base-100 p-6 rounded-3xl shadow-lg border border-base-300
    transition-all duration-500 hover:shadow-xl relative z-10 overflow-hidden;
  }

  /* Feature Container */
  .auth-feature-container {
    @apply hidden lg:flex flex-1 bg-gradient-to-br from-primary/20 to-secondary/20 
    items-center justify-center p-6 relative z-10;
  }

  .auth-feature-card {
    @apply max-w-lg bg-base-100/80 backdrop-blur-lg p-8 rounded-3xl shadow-xl 
    border border-base-300 transition-all duration-500 hover:shadow-2xl;
  }

  /* Branding Elements */
  .auth-branding {
    @apply text-center flex flex-col items-center gap-2 mb-6;
  }

  .auth-logo-container {
    @apply p-4 rounded-full bg-primary/10 transition-all duration-500 
    transform hover:scale-110 hover:rotate-6 group-hover:bg-primary/20;
  }

  .auth-logo-icon {
    @apply h-12 w-12 text-primary animate-pulse;
  }

  .auth-title {
    @apply text-3xl font-bold mt-4 bg-gradient-to-r from-primary to-secondary 
    bg-clip-text text-transparent transition-all duration-300;
  }

  .auth-subtitle {
    @apply text-lg text-base-content/70 mt-2;
  }

  /* Form Elements */
  .auth-form {
    @apply space-y-8;
  }

  .form-fields {
    @apply space-y-4;
  }

  .form-field {
    @apply space-y-1;
  }

  .form-field-header {
    @apply flex justify-between items-center;
  }

  .form-field-label {
    @apply text-base font-medium;
  }

  .form-field-link {
    @apply text-primary hover:underline text-sm transition-all duration-300;
  }

  .form-input-container {
    @apply relative transition-all duration-300 transform 
    border-2 border-base-300 rounded-xl overflow-hidden h-12;
  }

  .form-input-container.focused {
    @apply border-primary scale-[1.01] shadow-md;
  }

  .form-field-icon {
    @apply absolute left-4 top-1/2 -translate-y-1/2 h-5 w-5 text-base-content/40
    transition-all duration-300 z-10;
  }

  .form-input-container.focused .form-field-icon {
    @apply text-primary;
  }

  .form-input {
    @apply input w-full pl-12 pr-12 h-12 bg-base-100 border-none focus:outline-none
    transition-all duration-300 z-20;
  }

  .form-label {
    @apply absolute left-12 top-1/2 -translate-y-1/2 text-base-content/60 
    transition-all duration-300 pointer-events-none z-0;
  }

  .form-input-container.focused .form-label,
  .form-input-container.has-value .form-label {
    @apply -translate-y-8 text-xs text-primary;
  }

  .form-toggle-visibility {
    @apply absolute right-4 top-1/2 -translate-y-1/2 hover:text-primary 
    transition-colors duration-300 z-30;
  }

  .toggle-icon {
    @apply h-5 w-5 text-base-content/60 hover:text-primary transition-colors duration-300;
  }

  .form-hint {
    @apply text-xs text-base-content/60 mt-2 ml-1;
  }

  /* Button Styling */
  .auth-submit-button {
    @apply btn w-full h-14 text-lg font-semibold flex items-center justify-center gap-2
    transition-all duration-500 bg-gradient-to-r from-primary to-secondary hover:scale-[1.02] 
    hover:shadow-lg text-white border-none;
  }

  .auth-btn-text {
    @apply transition-all duration-300;
  }

  .auth-btn-icon {
    @apply w-5 h-5 ml-1 transition-all duration-500;
  }

  .auth-submit-button:hover .auth-btn-icon {
    @apply transform translate-x-1;
  }

  .auth-loading-icon {
    @apply h-5 w-5 animate-spin;
  }

  .auth-submit-button.loading {
    @apply bg-primary/80 cursor-not-allowed;
  }

  .auth-submit-button.success {
    @apply bg-success;
  }

  /* Alt Action */
  .auth-alt-action {
    @apply text-center pt-4 border-t border-base-300 mt-6 text-base-content/70;
  }

  .auth-alt-link {
    @apply text-primary hover:underline font-semibold transition-colors duration-300;
  }

  /* Feature Content */
  .auth-feature-icon-container {
    @apply p-6 bg-gradient-to-br from-primary/10 to-secondary/10 rounded-full shadow-xl mx-auto
    transform transition-transform duration-500 hover:rotate-3 hover:scale-105 mb-8;
  }

  .auth-feature-icon {
    @apply h-16 w-16 text-primary transition-all duration-500;
  }

  .auth-feature-title {
    @apply text-3xl font-bold mb-4 text-center bg-gradient-to-r from-primary to-secondary 
    bg-clip-text text-transparent;
  }

  .auth-feature-description {
    @apply text-lg text-base-content/80 mb-6 text-center;
  }

  .auth-features-grid {
    @apply grid grid-cols-2 gap-4 mt-8;
  }

  .auth-features-list {
    @apply flex flex-col space-y-3 mt-6;
  }

  .auth-feature-item {
    @apply flex items-center gap-4 p-4 rounded-xl transform 
    transition-all duration-300 hover:scale-[1.02];
  }

  .auth-feature-item.primary-feature {
    @apply bg-primary/10;
  }

  .auth-feature-item.secondary-feature {
    @apply bg-secondary/10;
  }

  .auth-feature-item-icon-container {
    @apply p-3 rounded-full transition-all duration-300;
  }

  .primary-feature .auth-feature-item-icon-container {
    @apply bg-primary/20;
  }

  .secondary-feature .auth-feature-item-icon-container {
    @apply bg-secondary/20;
  }

  .auth-feature-item-icon {
    @apply h-6 w-6 transition-all duration-300;
  }

  .primary-feature .auth-feature-item-icon {
    @apply text-primary;
  }

  .secondary-feature .auth-feature-item-icon {
    @apply text-secondary;
  }

  .auth-feature-item-title {
    @apply font-semibold;
  }

  .auth-feature-item-description {
    @apply text-base-content/70 text-sm;
  }

  .auth-feature-item-text {
    @apply font-medium;
  }

  /* Success Message */
  .success-animation {
    @apply animate-pulse;
  }

  .auth-success-message {
    @apply flex flex-col items-center justify-center py-8 space-y-4;
  }

  .auth-success-icon-container {
    @apply p-4 bg-success/20 rounded-full mb-4;
  }

  .auth-success-icon {
    @apply h-12 w-12 text-success;
  }

  .auth-success-title {
    @apply text-2xl font-bold text-success;
  }

  .auth-success-text {
    @apply text-base-content/70 text-center mb-6;
  }

  .auth-success-button {
    @apply btn btn-success flex items-center justify-center gap-2 px-6;
  }
}

/* Toast Customization */
.Toastify__toast {
  @apply rounded-xl border border-base-300 shadow-lg;
}

/* Animations */
@keyframes float {
  0% { transform: translateY(0px); }
  50% { transform: translateY(-20px); }
  100% { transform: translateY(0px); }
}

.float-animation {
  animation: float 6s ease-in-out infinite;
}

@keyframes pulse-glow {
  0% { box-shadow: 0 0 0 0 rgba(var(--p), 0.4); }
  70% { box-shadow: 0 0 0 10px rgba(var(--p), 0); }
  100% { box-shadow: 0 0 0 0 rgba(var(--p), 0); }
}

.pulse-glow {
  animation: pulse-glow 2s infinite;
}

/* Responsive Adjustments */
@media (max-width: 1023px) {
  .auth-feature-container {
    @apply hidden;
  }
}

@media (min-height: 800px) {
  .auth-form-card {
    @apply space-y-8;
  }
  
  .form-fields {
    @apply space-y-6;
  }
}

/* DatePicker Styles */
.react-datepicker {
  font-family: inherit !important;
  border: 1px solid #374151 !important;
  border-radius: 1rem !important;
  overflow: hidden;
  font-size: 0.9rem !important;
  background-color: white !important;
  box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1) !important;
  display: flex !important;
}

/* Fix month container width */
.react-datepicker__month-container {
  width: 300px !important;
  background-color: white !important;
}

/* Improve time container styling */
.react-datepicker__time-container {
  width: 150px !important;
  border-left: 1px solid #e5e7eb !important;
  background-color: white !important;
}

.react-datepicker__time-box {
  width: 100% !important;
  text-align: center !important;
}

/* Improve header and navigation */
.react-datepicker__header {
  background-color: #f3f4f6 !important;
  border-bottom: 1px solid #e5e7eb !important;
  padding: 1rem 0.5rem !important;
  position: relative !important;
}

.react-datepicker__current-month {
  color: #111827 !important;
  font-weight: 600 !important;
  margin-bottom: 0.5rem !important;
  padding: 0 2rem !important; /* Make space for navigation arrows */
}

/* Fix navigation arrows */
.react-datepicker__navigation {
  top: 1rem !important;
}

.react-datepicker__navigation--previous {
  left: 7px !important;
}

.react-datepicker__navigation--next {
  right: 7px !important;
}

.react-datepicker__navigation-icon::before {
  border-color: #4b5563 !important;
  border-width: 2px 2px 0 0 !important;
  height: 9px !important;
  width: 9px !important;
}

/* Improve time picker items */
.react-datepicker__time-list {
  padding: 0 !important;
  display: grid !important;
  grid-template-columns: repeat(2, 1fr) !important;
  height: 300px !important;
  overflow-y: auto !important;
}

.react-datepicker__time-list-item {
  height: auto !important;
  padding: 0.5rem !important;
  color: #111827 !important;
  display: flex !important;
  justify-content: center !important;
  align-items: center !important;
  font-size: 0.875rem !important;
  border-radius: 0.375rem !important;
  margin: 2px !important;
}

.react-datepicker__time-list-item:hover {
  background-color: #f3f4f6 !important;
}

.react-datepicker__time-list-item--selected {
  background-color: #2563eb !important;
  color: white !important;
  font-weight: 500 !important;
}

/* Dark mode adjustments */
[data-theme="dark"] .react-datepicker {
  background-color: #1f2937 !important;
  border-color: #374151 !important;
}

[data-theme="dark"] .react-datepicker__month-container,
[data-theme="dark"] .react-datepicker__time-container {
  background-color: #1f2937 !important;
}

[data-theme="dark"] .react-datepicker__time-list-item {
  color: #f3f4f6 !important;
}

[data-theme="dark"] .react-datepicker__time-list-item:hover {
  background-color: #374151 !important;
}

/* Time section header */
.react-datepicker__header--time {
  padding: 0.5rem !important;
  text-align: center !important;
  background-color: #f3f4f6 !important;
  border-bottom: 1px solid #e5e7eb !important;
}

[data-theme="dark"] .react-datepicker__header--time {
  background-color: #374151 !important;
  border-color: #4b5563 !important;
}

/* Ensure the calendar container doesn't overflow */
.react-datepicker {
  position: relative !important;
  max-height: 400px !important;
  overflow: auto !important;
}

/* Adjust time container height */
.react-datepicker__time-container {
  max-height: 400px !important;
}

.react-datepicker__time-box {
  max-height: 350px !important;
}

/* Keep your existing DatePicker styles */
















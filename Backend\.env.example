# MongoDB Atlas Connection
MONGODB_URI=mongodb+srv://your_username:<EMAIL>/?retryWrites=true&w=majority&appName=Cluster0

# Server Configuration
PORT=10000
JWT_SECRET=your_super_secret_jwt_key_here
NODE_ENV=production

# Cloudinary Configuration
CLOUDINARY_CLOUD_NAME=your_cloudinary_cloud_name
CLOUDINARY_API_KEY=your_cloudinary_api_key
CLOUDINARY_API_SECRET=your_cloudinary_api_secret

# Google OAuth Configuration
GOOGLE_CLIENT_ID=your_google_client_id
GOOGLE_CLIENT_SECRET=your_google_client_secret
GOOGLE_CALLBACK_URL=https://your-app-name.onrender.com/api/auth/google/callback

# Frontend URL
FRONTEND_URL=https://your-app-name.onrender.com

# Instructions:
# 1. Copy this file to .env
# 2. Replace all placeholder values with your actual values
# 3. Update the Render URLs with your actual app domain
# 4. Make sure to set these same environment variables in your Render dashboard

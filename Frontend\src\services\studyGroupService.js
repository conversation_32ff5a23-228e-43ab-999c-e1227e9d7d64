import { axiosInstance } from "../lib/axios";

export const studyGroupService = {
  // Get all study groups with filters
  getGroups: async (filters = {}) => {
    const queryString = new URLSearchParams(filters).toString();
    return axiosInstance.get(`/api/study-groups?${queryString}`);
  },

  // Create new study group
  createGroup: async (groupData) => {
    return axiosInstance.post("/api/study-groups", groupData);
  },

  // Join or request to join a group
  joinGroup: async (groupId) => {
    try {
      const response = await axiosInstance.post(`/api/study-groups/${groupId}/join`);
      return response;
    } catch (error) {
      console.error("Service join group error:", error);
      throw error;
    }
  },

  // Update group details (admin only)
  updateGroup: async (groupId, updates) => {
    return axiosInstance.put(`/api/study-groups/${groupId}`, updates);
  },

  // Leave or delete group
  leaveGroup: async (groupId) => {
    try {
      const response = await axiosInstance.post(`/api/study-groups/${groupId}/leave`);
      return response.data;
    } catch (error) {
      console.error('Error leaving group:', error);
      throw error;
    }
  },

  // Get group messages
  getGroupMessages: async (groupId, page = 1) => {
    return axiosInstance.get(`/api/group-messages/${groupId}?page=${page}`);
  },

  // Send group message
  sendGroupMessage: async (groupId, message) => {
    return axiosInstance.post(`/api/group-messages/${groupId}`, message);
  }
};



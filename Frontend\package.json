{"name": "frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@daily-co/daily-js": "^0.75.2", "axios": "^1.7.7", "jwt-encode": "^1.0.1", "lucide-react": "^0.459.0", "react": "^18.3.1", "react-datepicker": "^4.25.0", "react-dom": "^18.3.1", "react-hot-toast": "^2.4.1", "react-icons": "^5.5.0", "react-router-dom": "^6.29.0", "socket.io-client": "^4.8.1", "zustand": "^5.0.1"}, "devDependencies": {"@eslint/js": "^9.13.0", "@types/react": "^18.3.12", "@types/react-dom": "^18.3.1", "@vitejs/plugin-react": "^4.3.3", "autoprefixer": "^10.4.20", "daisyui": "^4.12.14", "eslint": "^9.13.0", "eslint-plugin-react": "^7.37.2", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.14", "globals": "^15.11.0", "postcss": "^8.4.49", "tailwindcss": "^3.4.15", "vite": "^5.4.10"}}
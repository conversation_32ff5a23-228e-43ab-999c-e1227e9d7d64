{"name": "backend", "version": "1.0.0", "description": "", "main": "src/index.js", "scripts": {"dev": "nodemon src/index.js", "start": "node src/index.js"}, "keywords": [], "author": "", "type": "module", "license": "ISC", "dependencies": {"bcryptjs": "^2.4.3", "cloudinary": "^2.5.1", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "dotenv": "^16.4.5", "express": "^4.21.1", "jsonwebtoken": "^9.0.2", "mongoose": "^8.8.1", "passport": "^0.6.0", "passport-google-oauth20": "^2.0.0", "socket.io": "^4.8.1", "uuid": "^9.0.0"}, "devDependencies": {"nodemon": "^3.1.7"}}